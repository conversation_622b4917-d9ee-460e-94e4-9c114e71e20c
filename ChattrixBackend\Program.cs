using Amazon.Extensions.NETCore.Setup;
using Amazon.S3;
using ChattrixBackend;
using ChattrixBackend.Core.Entities.UserManagement.EmailServiceModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;
using ChattrixBackend.EntityFramworkCore.Data;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Server.Core.MappingProfile;
using System.Text;

var builder = WebApplication.CreateBuilder(args);
IConfigurationSection JWTSetting = builder.Configuration.GetSection("JWTSetting");
byte[] key = Encoding.UTF8.GetBytes(JWTSetting['securityKey']);

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(UserManagementMappingProfile));
// Add services to the container.

builder.Services.AddControllers();

// Enable CORS
builder.Services.AddCors(options => {
    options.AddPolicy("AllowAngularApp",
        policy => {
            policy.AllowAnyOrigin()
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        });
});
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c => {
    c.SwaggerDoc("v1", new OpenApiInfo {
        Title = "Chattrix Backend API",
        Version = "v1",
        Description = "API endpoints for Chattrix Backend application"
    });

    // Add Bearer token support in Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme {
        In = ParameterLocation.Header,
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Bearer {token}\"",
        Name = "Authorization",
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header
            },
            new List<string>()
        }
    });
});

builder.Services.AddAuthentication(options => {
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    //options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(options => {
    options.SaveToken = false;
    options.RequireHttpsMetadata = false;
    options.TokenValidationParameters = new TokenValidationParameters {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };

    // Configure JWT events to prevent redirects and return proper 401 responses
    options.Events = new JwtBearerEvents {
        OnChallenge = context => {
            // Skip the default logic and return 401 instead of redirecting
            context.HandleResponse();
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json";
            var result = System.Text.Json.JsonSerializer.Serialize(new {
                isSuccess = false,
                message = "Unauthorized access. Please provide a valid token.",
                statusCode = 401
            });
            return context.Response.WriteAsync(result);
        },
        OnForbidden = context => {
            context.Response.StatusCode = 403;
            context.Response.ContentType = "application/json";
            var result = System.Text.Json.JsonSerializer.Serialize(new {
                isSuccess = false,
                message = "Access forbidden. You don't have permission to access this resource.",
                statusCode = 403
            });
            return context.Response.WriteAsync(result);
        }
    };
});

builder.Services.AddAuthorization();

var emailConfig = builder.Configuration.GetSection("EmailConfiguration").Get<EmailConfiguration>();
builder.Services.AddSingleton(emailConfig);

//DB config
builder.Services.AddDbContext<ApplicationDbContext>(options =>
options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

//Identity config
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options => {
    // Configure Identity to work with JWT authentication
    options.User.RequireUniqueEmail = true;
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure Identity to not use cookies and not redirect
builder.Services.ConfigureApplicationCookie(options => {
    options.Events.OnRedirectToLogin = context => {
        context.Response.StatusCode = 401;
        return Task.CompletedTask;
    };
    options.Events.OnRedirectToAccessDenied = context => {
        context.Response.StatusCode = 403;
        return Task.CompletedTask;
    };
});

// Configure AWS S3
builder.Services.AddAWSService<IAmazonS3>(new AWSOptions {
    Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:Region"] ?? "eu-north-1"),
    Credentials = new Amazon.Runtime.BasicAWSCredentials(
        builder.Configuration["AWS:S3:AccessKey"] ?? throw new ArgumentNullException("AWS:S3:AccessKey is missing in configuration"),
        builder.Configuration["AWS:S3:SecretKey"] ?? throw new ArgumentNullException("AWS:S3:SecretKey is missing in configuration"))
});

// Register all Chattrix services (after IAmazonS3)
builder.Services.AddChattrixServices();

var app = builder.Build();

// Configure WebSocket support
app.UseWebSockets(new WebSocketOptions {
    KeepAliveInterval = TimeSpan.FromMinutes(2)
});

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment()) {
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAngularApp");

app.UseAuthentication();
app.UseAuthorization();

// Add WebSocket middleware
var tokenValidationParameters = new TokenValidationParameters {
    ValidateIssuer = true,
    ValidateAudience = true,
    ValidateLifetime = true,
    ValidateIssuerSigningKey = true,
    ValidAudience = JWTSetting["ValidAudience"],
    ValidIssuer = JWTSetting["ValidIssuer"],
    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(JWTSetting.GetSection("securityKey").Value))
};

//app.UseMiddleware<WebSocketMiddleware>(tokenValidationParameters);

app.MapControllers();

app.Run();
